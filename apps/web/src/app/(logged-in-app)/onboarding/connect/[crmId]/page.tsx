"use client";

import { notFound } from "next/navigation";

import { CRMConnectionStep } from "~/features/onboarding/components/CRMConnectionStep";

interface ConnectCRMPageProps {
  params: {
    crmId: string;
  };
}

const SUPPORTED_CRMS = ["salesforce", "hubspot"];

export default function ConnectCRMPage({ params }: ConnectCRMPageProps) {
  const { crmId } = params;

  if (!SUPPORTED_CRMS.includes(crmId)) {
    notFound();
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      <CRMConnectionStep crmId={crmId} />
    </div>
  );
}
