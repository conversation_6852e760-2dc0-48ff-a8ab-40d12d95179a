import { ErrorStep } from "~/features/salesforce/components/ErrorStep";
import { LoadingStep } from "~/features/salesforce/components/LoadingStep";
import { SuccessStep } from "~/features/salesforce/components/SuccessStep";

interface CRMCallbackContentProps {
  crmId: string;
  isLoading: boolean;
  isImporting: boolean;
  isSuccess: boolean;
  isError: boolean;
  errorMessage: string | null;
}

const CRM_CONFIGS = {
  salesforce: {
    displayName: "Salesforce",
    connectingTitle: "Processing Salesforce connection...",
    importingTitle: "Synchronizing your accounts...",
  },
  hubspot: {
    displayName: "HubSpot",
    connectingTitle: "Processing HubSpot connection...",
    importingTitle: "Synchronizing your contacts...",
  },
};

export const CRMCallbackContent = ({
  crmId,
  isLoading,
  isImporting,
  isSuccess,
  isError,
  errorMessage,
}: CRMCallbackContentProps) => {
  const config = CRM_CONFIGS[crmId as keyof typeof CRM_CONFIGS];
  
  if (!config) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-6">
        <p className="text-red-500">Unsupported CRM: {crmId}</p>
      </div>
    );
  }

  let content = null;

  if (isLoading) {
    content = (
      <LoadingStep
        title={config.connectingTitle}
        stage="connecting"
      />
    );
  } else if (isImporting) {
    content = (
      <LoadingStep 
        title={config.importingTitle} 
        stage="importing" 
      />
    );
  } else if (isSuccess) {
    content = <SuccessStep />;
  } else if (isError) {
    content = <ErrorStep errorMessage={errorMessage} />;
  }

  if (!content) {
    return null;
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      {content}
    </div>
  );
};
