"use client";

import { ArrowRight } from "lucide-react";
import Image from "next/image";
import { useMemo } from "react";

import type { Integration } from "@pearl/api-hooks";
import { useGetIntegrations } from "@pearl/api-hooks";

import { Button } from "~/components/ui/Button";
import { useIntegrationActions } from "~/features/integrations/hooks/useIntegrationActions";
import { getStrategy } from "~/features/integrations/registry";

interface CRMConnectionStepProps {
  crmId: string;
}

interface CRMConfig {
  displayName: string;
  logoPath: string;
  description: string;
}

const CRM_CONFIGS: Record<string, CRMConfig> = {
  salesforce: {
    displayName: "Salesforce",
    logoPath: "/integrations/salesforce-logo.png",
    description:
      "Connecting to Salesforce allows <PERSON> to access your pipeline, understand your accounts, and surface relevant insights—so you don't have to repeat yourself or dig through scattered notes.",
  },
  hubspot: {
    displayName: "HubSpot",
    logoPath: "/integrations/hubspot.png",
    description:
      "Connecting to HubSpot allows <PERSON> to access your contacts, deals, and sales pipeline to provide personalized insights and help you stay organized.",
  },
};

export function CRMConnectionStep({ crmId }: CRMConnectionStepProps) {
  const { data: integrations, isLoading: integrationsLoading } =
    useGetIntegrations();

  const integration = useMemo(() => {
    if (!integrations) return null;

    // Look for the CRM in available integrations
    const availableIntegration = integrations.availableIntegrations.find(
      (int) => int.id === crmId,
    );

    if (availableIntegration) return availableIntegration;

    // If not found in available, create a mock integration for the strategy
    const strategy = getStrategy(crmId);
    return {
      id: crmId,
      name: strategy.displayName ?? crmId,
      description: "",
      integrationType: "crm",
      source: "",
      isActive: false,
    } as Integration;
  }, [integrations, crmId]);

  const { connect, isConnecting, extraLoading } = useIntegrationActions(
    integration || {
      id: crmId,
      name: crmId,
      description: "",
      integrationType: "crm",
      source: "",
      isActive: false,
    },
  );

  const crmConfig = CRM_CONFIGS[crmId];
  const isLoading = integrationsLoading || extraLoading || isConnecting;

  if (!crmConfig) {
    return (
      <div className="mx-auto flex h-[400px] max-w-md flex-col items-center justify-center">
        <p className="text-red-500">Unsupported CRM: {crmId}</p>
      </div>
    );
  }

  const handleConnect = () => {
    connect();
  };

  return (
    <div className="mx-auto flex h-[400px] max-w-md flex-col items-center justify-between">
      <div className="w-full text-center">
        <div className="mb-4 flex justify-center">
          <div className="flex h-[90px] w-[90px] items-center justify-center">
            <Image
              src={crmConfig.logoPath}
              alt={`${crmConfig.displayName} Logo`}
              width={72}
              height={72}
              className="max-h-full max-w-full object-contain"
            />
          </div>
        </div>
        <h1 className="mb-8 text-2xl font-bold">
          Connect to {crmConfig.displayName}
        </h1>
      </div>

      <div className="mb-auto w-full max-w-md flex-grow overflow-auto text-center">
        <p className="mb-4 text-gray-600">{crmConfig.description}</p>
        <p className="mb-8 text-sm text-gray-500">
          Your data stays secure and only you can see it.
        </p>
      </div>

      <div className="h-[60px] w-full max-w-md">
        <Button
          onClick={handleConnect}
          className="w-full"
          size="lg"
          disabled={isLoading}
        >
          {isLoading ? "Loading..." : `Connect with ${crmConfig.displayName}`}
          {!isLoading && <ArrowRight size={16} className="ml-2" />}
        </Button>
      </div>
    </div>
  );
}
